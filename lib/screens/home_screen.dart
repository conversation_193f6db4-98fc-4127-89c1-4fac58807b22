import 'package:flutter/material.dart';
import 'package:packagingwala/widgets/custom_text_field.dart';
import 'package:packagingwala/widgets/platform_icon.dart';
import 'package:packagingwala/constants/app_colors.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int selectedTabIndex = 0;
  final List<String> tabs = ['All Orders', 'Pending Orders', 'Processed Orders'];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 16),

              // Profile and Notification
              Row(
                children: [
                  CircleAvatar(
                    radius: 28,
                    backgroundColor: AppColors.primaryColor.withValues(alpha: 0.1),
                    child: const PlatformIcon(
                      iconName: 'person',
                      size: 32,
                      color: AppColors.primaryColor,
                    ),
                  ),
                  const SizedBox(width: 12),
                  const Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "Hi Krishna",
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Row(
                        children: [
                          PlatformIcon(
                            iconName: 'location',
                            size: 14,
                            color: AppColors.primaryColor,
                          ),
                          SizedBox(width: 4),
                          Text(
                            "Delhi",
                            style: TextStyle(fontSize: 14, color: AppColors.greyColor),
                          ),
                        ],
                      ),
                    ],
                  ),
                  const Spacer(),
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          blurRadius: 6,
                          color: Colors.black.withValues(alpha: 0.05),
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: IconButton(
                      icon: const PlatformIcon(
                        iconName: 'notifications',
                        color: AppColors.primaryColor,
                      ),
                      onPressed: () {},
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 20),

              // Search Box
              CustomTextField(
                hintText: "Search Orders",
                prefixIcon: const PlatformIcon(
                  iconName: 'search',
                  color: AppColors.primaryColor,
                ),
                fillColor: Colors.white,
                borderColor: AppColors.primaryColor,
                focusedBorderColor: AppColors.primaryColor,
                borderRadius: 12,
                contentPadding: const EdgeInsets.symmetric(vertical: 12),
                onChanged: (value) {
                },
              ),

              const SizedBox(height: 20),

              // Tabs
              SizedBox(
                height: 40,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: tabs.length,
                  itemBuilder: (context, index) {
                    return _buildTab(
                      tabs[index],
                      isSelected: selectedTabIndex == index,
                      onTap: () {
                        setState(() {
                          selectedTabIndex = index;
                        });
                      },
                    );
                  },
                ),
              ),

              const SizedBox(height: 20),

              // Orders Grid
              Expanded(
                child: GridView.count(
                  crossAxisCount: 2,
                  crossAxisSpacing: 12,
                  mainAxisSpacing: 12,
                  childAspectRatio: 0.7,
                  children: List.generate(6, (index) {
                    return _buildOrderCard(index);
                  }),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTab(String label, {bool isSelected = false, VoidCallback? onTap}) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 9),
        margin: const EdgeInsets.only(right: 10),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primaryColor : const Color(0xFFEFEFEF),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Text(
          label,
          textAlign: TextAlign.center,
          style: TextStyle(
            color: isSelected ? AppColors.blackColor : AppColors.greyColor,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  Widget _buildOrderCard(int index) {
    final images = [
      'assets/images/image1.png',
      'assets/images/image1.png',
      'assets/images/image1.png',
      'assets/images/image1.png',
      'assets/images/image1.png',
      'assets/images/image1.png',
    ];
    final ids = [
      'ORD-2024-0001',
      'ORD-2024-0002',
      'ORD-2024-0003',
      'ORD-2024-0004',
      'ORD-2024-0005',
      'ORD-2024-0006',
    ];
    final dates = [
      'June 4, 2025',
      'June 7, 2025',
      'June 9, 2025',
      'June 10, 2025',
      'June 11, 2025',
      'June 12, 2025',
    ];

    return GestureDetector(
      onTap: () {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Tapped on ${ids[index]}'),
            duration: const Duration(seconds: 1),
          ),
        );
      },
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(14),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ClipRRect(
              borderRadius: const BorderRadius.vertical(top: Radius.circular(14)),
              child: Image.asset(
                images[index],
                height: 130,
                width: double.infinity,
                fit: BoxFit.cover,
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(10),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'ID: ${ids[index]}',
                    style: const TextStyle(
                      fontWeight: FontWeight.w500,
                      fontSize: 13,
                    ),
                  ),
                  const SizedBox(height: 6),
                  Row(
                    children: [
                      const PlatformIcon(
                        iconName: 'calendar',
                        size: 14,
                        color: AppColors.primaryColor,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        dates[index],
                        style: TextStyle(
                          fontSize: 12,
                          color: AppColors.greyColor,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
