import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:packagingwala/widgets/custom_app_bar.dart';
import 'package:packagingwala/constants/app_colors.dart';

class OrderDetailsScreen extends StatefulWidget {
  final String orderId;
  final String orderDate;
  final String orderImage;

  const OrderDetailsScreen({
    super.key,
    required this.orderId,
    required this.orderDate,
    required this.orderImage,
  });

  @override
  State<OrderDetailsScreen> createState() => _OrderDetailsScreenState();
}

class _OrderDetailsScreenState extends State<OrderDetailsScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      body: SafeArea(
        child: Column(
          children: [
            CustomAppBar(
              title: 'Order Details',
              showBackButton: true,
            ),

            // Scrollable Content
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Order Header
                    _buildOrderHeader(),

                    const SizedBox(height: 24),

                    // Order Tracking
                    _buildOrderTracking(),

                    const SizedBox(height: 24),

                    // Order Details
                    _buildOrderDetails(),

                    const SizedBox(height: 24),

                    // Shipping Address
                    _buildShippingAddress(),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Image.asset(
              widget.orderImage,
              width: 60,
              height: 60,
              fit: BoxFit.cover,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'ID: ${widget.orderId}',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: AppColors.blackColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Placed On ${widget.orderDate}',
                  style: TextStyle(
                    fontSize: 14,
                    color: AppColors.greyColor,
                  ),
                ),
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppColors.primaryColor,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Text(
                    'On Track',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderTracking() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Order Tracking',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: AppColors.blackColor,
          ),
        ),
        const SizedBox(height: 16),
        _buildTrackingStep(
          icon: 'assets/icons/packing_icon.svg',
          title: 'Packing',
          subtitle: '4 June 2025, 10:30 AM',
          description: 'Order items are being Packed',
          isCompleted: true,
          isActive: false,
        ),
        _buildTrackingStep(
          icon: 'assets/icons/designing_icon.svg',
          title: 'Designing',
          subtitle: '4 June 2025, 11:30 AM',
          description: 'Custom Design Being Created',
          isCompleted: false,
          isActive: false,
        ),
        _buildTrackingStep(
          icon: 'assets/icons/sorting_icon.svg',
          title: 'Sorting',
          subtitle: '4 June 2025, 2:30 PM',
          description: 'Order items are being organized',
          isCompleted: false,
          isActive: false,
        ),
        _buildTrackingStep(
          icon: 'assets/icons/onboarding_icon.svg',
          title: 'On-boarding',
          subtitle: '5 June 2025, 9:30 AM',
          description: 'Order items into Production System',
          isCompleted: false,
          isActive: false,
        ),
        _buildTrackingStep(
          icon: 'assets/icons/design_approved_icon.svg',
          title: 'Design Approved',
          subtitle: '5 June 2025, 11:30 AM',
          description: 'Design has been approved for production',
          isCompleted: false,
          isActive: false,
        ),
        _buildTrackingStep(
          icon: 'assets/icons/heating_icon.svg',
          title: 'Heating',
          subtitle: '6 June 2025, 8:30 AM',
          description: 'Advanced design modifications',
          isCompleted: false,
          isActive: false,
        ),
        _buildTrackingStep(
          icon: 'assets/icons/under_design_icon.svg',
          title: 'Under Designing',
          subtitle: '6 June 2025, 9:40 AM',
          description: 'Heat treatment process in progress',
          isCompleted: false,
          isActive: false,
        ),
        _buildTrackingStep(
          icon: 'assets/icons/ready_to_dispatch_icon.svg',
          title: 'Metallic Pasting',
          subtitle: '6 June 2025, 10:30 AM',
          description: 'Metallic coating application',
          isCompleted: false,
          isActive: false,
        ),
        _buildTrackingStep(
          icon: 'assets/icons/ready_to_dispatch_icon.svg',
          title: 'Ready to Dispatch',
          subtitle: '7 June 2025, 2:30 PM',
          description: 'Order ready for shipping',
          isCompleted: false,
          isActive: false,
        ),
        _buildTrackingStep(
          icon: 'assets/icons/ready_to_dispatch_icon.svg',
          title: 'Dispatching',
          subtitle: '7 June 2025, 10:30 AM',
          description: 'Final packaging completed',
          isCompleted: false,
          isActive: false,
        ),
        _buildTrackingStep(
          icon: 'assets/icons/ld_pasting_icon.svg',
          title: 'LD Pasting',
          subtitle: '8 June 2025, 2:30 PM',
          description: 'LD pasting operations',
          isCompleted: false,
          isActive: false,
        ),
        _buildTrackingStep(
          icon: 'assets/icons/polyester_printing_icon.svg',
          title: 'Polyester Printing',
          subtitle: '9 June 2025, 9:30 AM',
          description: 'Polyester printing in progress',
          isCompleted: false,
          isActive: false,
          isLast: true,
        ),
      ],
    );
  }

  Widget _buildTrackingStep({
    required String icon,
    required String title,
    required String subtitle,
    required String description,
    required bool isCompleted,
    required bool isActive,
    bool isLast = false,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Timeline indicator
        Column(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: isCompleted || isActive
                    ? AppColors.primaryColor
                    : AppColors.greyColor.withValues(alpha: 0.3),
                shape: BoxShape.circle,
              ),
              child: Center(
                child: SvgPicture.asset(
                  icon,
                  width: 20,
                  height: 20,
                  colorFilter: ColorFilter.mode(
                    isCompleted || isActive ? AppColors.blackColor : AppColors.greyColor,
                    BlendMode.srcIn,
                  ),
                ),
              ),
            ),
            if (!isLast)
              Container(
                width: 2,
                height: 40,
                color: isCompleted
                    ? AppColors.primaryColor
                    : AppColors.greyColor.withValues(alpha: 0.3),
              ),
          ],
        ),
        const SizedBox(width: 12),
        // Content
        Expanded(
          child: Container(
            padding: const EdgeInsets.only(bottom: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: isCompleted || isActive
                            ? AppColors.blackColor
                            : AppColors.greyColor,
                      ),
                    ),
                    Text(
                      'View Details',
                      style: TextStyle(
                        fontSize: 12,
                        color: AppColors.primaryColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: 12,
                    color: AppColors.greyColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 12,
                    color: AppColors.greyColor,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildOrderDetails() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Order Details',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: AppColors.blackColor,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Image.asset(
                  widget.orderImage,
                  width: 60,
                  height: 60,
                  fit: BoxFit.cover,
                ),
              ),
              const SizedBox(width: 12),
              const Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Paper Order',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: AppColors.blackColor,
                      ),
                    ),
                    SizedBox(height: 4),
                    Text(
                      'Quantity: 2 Items',
                      style: TextStyle(
                        fontSize: 14,
                        color: AppColors.greyColor,
                      ),
                    ),
                    SizedBox(height: 4),
                    Text(
                      '₹ 450',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: AppColors.primaryColor,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildShippingAddress() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Shipping Address',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: AppColors.blackColor,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: const Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Milan Goyal',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.blackColor,
                ),
              ),
              SizedBox(height: 8),
              Text(
                '1206, 12th Floor, N26, Jaypee Aman, Sector 151, Noida, 201310',
                style: TextStyle(
                  fontSize: 14,
                  color: AppColors.greyColor,
                  height: 1.4,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}