import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:packagingwala/screens/profile/help_center_screen.dart';
import 'package:packagingwala/widgets/custom_app_bar.dart';
import 'package:packagingwala/widgets/platform_icon.dart';
import 'package:packagingwala/constants/app_colors.dart';
import 'package:permission_handler/permission_handler.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  bool _notificationEnabled = false;

  @override
  void initState() {
    super.initState();
    _checkNotificationPermission();
  }

  Future<void> _checkNotificationPermission() async {
    final status = await Permission.notification.status;
    log('Notification permission status: $status');
    setState(() {
      _notificationEnabled = status.isGranted;
    });
  }

  Future<void> _toggleNotification(bool value) async {
    if (value) {
      final status = await Permission.notification.request();
      setState(() {
        _notificationEnabled = status.isGranted;
      });
    } else {
      // Can't programmatically disable notifications, show dialog to go to settings
      _showNotificationSettingsDialog();
    }
  }

  void _showNotificationSettingsDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Notification Settings'),
          content: const Text(
            'To disable notifications, please go to your device settings.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                openAppSettings();
              },
              child: const Text('Open Settings'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      body: SafeArea(
        child: Column(
          children: [
            CustomAppBar(title: 'My Account', showBackButton: true),

            // Scrollable Content
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    // Profile Header
                    _buildProfileHeader(),

                    const SizedBox(height: 24),

                    // Menu Items
                    _profileSettings(),
                  ],
                ),
              ),
            ),

            // Logout Button at bottom
            _buildLogoutButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileHeader() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Profile Picture
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: AppColors.primaryColor,
              shape: BoxShape.circle,
            ),
            child: const Center(
              child: PlatformIcon(
                iconName: 'person',
                size: 30,
                color: Colors.white,
              ),
            ),
          ),

          const SizedBox(width: 16),

          // User Info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Milan Goyal',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: AppColors.blackColor,
                  ),
                ),

                const SizedBox(height: 4),

                Text(
                  '+91 9001136688',
                  style: TextStyle(fontSize: 14, color: AppColors.greyColor),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _profileSettings() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildMenuItem(
            svgIcon: 'assets/icons/notification_icon.svg',
            title: 'Notification settings',
            hasToggle: true,
            onTap: () {},
          ),
          const Divider(height: 1, thickness: 0.5, color: Colors.grey),
          _buildMenuItem(
            svgIcon: 'assets/icons/privacy_policy_icon.svg',
            title: 'Privacy Policy',
            onTap: () {},
          ),
          const Divider(height: 1, thickness: 0.5, color: Colors.grey),
          _buildMenuItem(
            svgIcon: 'assets/icons/support_icon.svg',
            title: 'Help Center',
            onTap: () {
              Navigator.push(context, MaterialPageRoute(builder: (context) => const HelpCenterScreen()));
            },
          ),
        ],
      ),
    );
  }

  Widget _buildMenuItem({
    String? icon,
    String? svgIcon,
    required String title,
    bool hasToggle = false,
    required VoidCallback onTap,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 12),
      child: InkWell(
        onTap: hasToggle ? null : onTap,
        child: Row(
          children: [
            svgIcon != null
                ? SvgPicture.asset(
                  svgIcon,
                  width: 24,
                  height: 24,
                  colorFilter: ColorFilter.mode(
                    AppColors.greyColor,
                    BlendMode.srcIn,
                  ),
                )
                : PlatformIcon(
                  iconName: icon!,
                  size: 20,
                  color: AppColors.greyColor,
                ),
            const SizedBox(width: 16),
            Expanded(
              child: Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: AppColors.blackColor,
                ),
              ),
            ),
            if (hasToggle)
              Transform.scale(
                scale: 0.75,
                child: Switch(
                  value: _notificationEnabled,
                  onChanged: _toggleNotification,
                  activeColor: AppColors.primaryColor,
                  materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
              )
            else
              const PlatformIcon(
                iconName: 'forward',
                size: 16,
                color: AppColors.greyColor,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildLogoutButton() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      child: OutlinedButton(
        onPressed: () {
          // Show logout confirmation dialog
          _showLogoutDialog();
        },
        style: OutlinedButton.styleFrom(
          foregroundColor: Colors.red,
          side: BorderSide(color: Colors.red.shade300),
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: const Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.logout, size: 20),
            SizedBox(width: 8),
            Text(
              'Log out',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
          ],
        ),
      ),
    );
  }

  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Logout'),
          content: const Text('Are you sure you want to logout?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('Logout'),
            ),
          ],
        );
      },
    );
  }
}
