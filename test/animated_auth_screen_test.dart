import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:packagingwala/screens/login/animated_auth_screen.dart';

void main() {
  group('AnimatedAuthScreen Tests', () {
    testWidgets('should display splash screen initially', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: AnimatedAuthScreen(),
        ),
      );

      // Verify splash screen elements are present
      expect(find.text('Get Started'), findsOneWidget);
      expect(find.text('Your Packaging Partner'), findsOneWidget);
      expect(find.byType(ElevatedButton), findsOneWidget);
    });

    testWidgets('should transition to login screen when Get Started is tapped', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: AnimatedAuthScreen(),
        ),
      );

      // Tap the Get Started button
      await tester.tap(find.text('Get Started'));
      await tester.pumpAndSettle();

      // Verify login screen elements are present
      expect(find.text('Login/Signup'), findsOneWidget);
      expect(find.text('Phone Number'), findsOneWidget);
      expect(find.text('Next'), findsOneWidget);
    });

    testWidgets('should transition to OTP screen when Next is tapped', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: AnimatedAuthScreen(),
        ),
      );

      // Navigate to login screen
      await tester.tap(find.text('Get Started'));
      await tester.pumpAndSettle();

      // Tap the Next button
      await tester.tap(find.text('Next'));
      await tester.pumpAndSettle();

      // Verify OTP screen elements are present
      expect(find.text('Verify Your Number'), findsOneWidget);
      expect(find.text("We've sent a 5-digit code to your phone number."), findsOneWidget);
      expect(find.text('Verify'), findsOneWidget);
    });

    testWidgets('should have buttons positioned at bottom of screen', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: AnimatedAuthScreen(),
        ),
      );

      // Verify Get Started button is present
      expect(find.text('Get Started'), findsOneWidget);

      // Navigate to login screen
      await tester.tap(find.text('Get Started'));
      await tester.pumpAndSettle();

      // Verify Next button is present
      expect(find.text('Next'), findsOneWidget);

      // Navigate to OTP screen
      await tester.tap(find.text('Next'));
      await tester.pumpAndSettle();

      // Verify Verify button is present
      expect(find.text('Verify'), findsOneWidget);
    });
  });
}
